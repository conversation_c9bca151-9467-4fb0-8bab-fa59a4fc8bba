import Image from "next/image";

export default function Home() {
  return (
    <div className="min-h-screen bg-white dark:bg-slate-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm z-50 border-b border-slate-200 dark:border-slate-700">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="text-xl font-semibold text-slate-800 dark:text-white">
              Neeharika Vemulapati
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#about" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">About</a>
              <a href="#experience" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Experience</a>
              <a href="#education" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Education</a>
              <a href="#achievements" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Achievements</a>
              <a href="#skills" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Skills</a>
              <a href="#projects" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Projects</a>
              <a href="#contact" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Contact</a>
            </div>
            <div className="md:hidden">
              <button className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 p-2">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-20 pb-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* Background with animated blobs */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
          <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
        </div>

        <div className="max-w-6xl mx-auto relative z-10">
          <div className="text-center">
            <div className="mb-8">
              <div className="w-40 h-40 mx-auto mb-6 rounded-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 p-1 animate-pulse hover:animate-spin transition-all duration-500 group">
                <div className="w-full h-full rounded-full overflow-hidden bg-slate-100 dark:bg-slate-800 hover:bg-white dark:hover:bg-slate-700 transition-colors duration-300">
                  <img
                    src="/neeharika-photo.jpg"
                    alt="Neeharika Vemulapati - Senior Software Engineer"
                    className="w-full h-full object-cover rounded-full group-hover:scale-110 transition-transform duration-500"
                  />
                </div>
              </div>
            </div>

            {/* Animated typing effect simulation */}
            <div className="mb-6">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-4 text-slate-900 dark:text-white animate-fade-in-up">
                Hi, I'm <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Neeharika Vemulapati</span>
                <span className="inline-block animate-bounce ml-2">👋</span>
              </h1>
            </div>

            <div className="space-y-4 mb-12 animate-fade-in-up animation-delay-500">
              <p className="text-xl sm:text-2xl text-slate-600 dark:text-slate-300 font-medium">
                💻 Senior Software Engineer
              </p>

              <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 px-6 py-3 rounded-full border border-blue-200 dark:border-blue-800">
                <span className="text-lg text-blue-700 dark:text-blue-300 font-semibold">
                  🏆 Microsoft AI Hackathon 2025 Winner
                </span>
                <span className="ml-2 animate-pulse">✨</span>
              </div>

              <p className="text-lg text-slate-600 dark:text-slate-300 max-w-4xl mx-auto leading-relaxed">
                🚀 Passionate about building innovative software solutions with expertise in
                <span className="font-semibold text-blue-600 dark:text-blue-400"> Java, Python, AWS</span>, and modern web technologies.
                Currently leading <span className="font-semibold text-purple-600 dark:text-purple-400">digital transformation initiatives</span> at CSAA Insurance Group.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16 animate-fade-in-up animation-delay-1000">
              <a
                href="#contact"
                className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                <span className="flex items-center justify-center">
                  📬 Get In Touch
                  <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </span>
              </a>
              <a
                href="#projects"
                className="group border-2 border-blue-600 text-blue-600 dark:text-blue-400 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-8 py-4 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                <span className="flex items-center justify-center">
                  🚀 View My Work
                  <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </span>
              </a>
            </div>

            {/* Enhanced Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-3xl mx-auto animate-fade-in-up animation-delay-1500">
              <div className="text-center group hover-lift">
                <div className="bg-white dark:bg-slate-800 p-6 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700 group-hover:border-blue-300 dark:group-hover:border-blue-600 transition-all duration-300">
                  <div className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">7+</div>
                  <div className="text-sm text-slate-600 dark:text-slate-300 font-medium">Years Experience</div>
                </div>
              </div>
              <div className="text-center group hover-lift">
                <div className="bg-white dark:bg-slate-800 p-6 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700 group-hover:border-purple-300 dark:group-hover:border-purple-600 transition-all duration-300">
                  <div className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">15+</div>
                  <div className="text-sm text-slate-600 dark:text-slate-300 font-medium">Major Projects</div>
                </div>
              </div>
              <div className="text-center group hover-lift">
                <div className="bg-white dark:bg-slate-800 p-6 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700 group-hover:border-pink-300 dark:group-hover:border-pink-600 transition-all duration-300">
                  <div className="text-4xl font-bold bg-gradient-to-r from-pink-600 to-red-600 bg-clip-text text-transparent mb-2">25+</div>
                  <div className="text-sm text-slate-600 dark:text-slate-300 font-medium">Technologies</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-900">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
              🌟 About Me
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              Senior Software Engineer with 7+ years of experience at Fortune 500 companies,
              specializing in enterprise applications, cloud architecture, and AI innovation. 💡
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                🚀 My Journey
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                My career spans from QA automation to senior engineering roles at AWS, American Express,
                FedEx, and CSAA Insurance Group. I've consistently earned recognition for technical
                excellence and leadership, including winning Microsoft's "Best Python Agent" award in 2025.
              </p>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Currently leading digital payments transformation at CSAA Insurance Group, I architect
                enterprise solutions using Java, Spring Boot, Python, and AWS. My expertise includes
                microservices, containerization, and modern CI/CD pipelines.
              </p>
              <p className="text-slate-600 dark:text-slate-300 mb-6">
                I'm passionate about AI innovation and contribute to healthcare technology through
                volunteer work at Phoenix Children's Hospital, building HIPAA-compliant solutions.
              </p>
            </div>

            <div className="space-y-6">
              <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg">
                <h4 className="font-semibold text-slate-900 dark:text-white mb-3">🎯 Current Focus</h4>
                <ul className="text-slate-600 dark:text-slate-300 space-y-2">
                  <li>💳 Digital Payments Transformation</li>
                  <li>☕ Enterprise Java & Spring Boot</li>
                  <li>☁️ AWS Cloud Architecture</li>
                  <li>🤖 AI Agents & Machine Learning</li>
                </ul>
              </div>

              <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg">
                <h4 className="font-semibold text-slate-900 dark:text-white mb-3">🏆 Key Achievements</h4>
                <ul className="text-slate-600 dark:text-slate-300 space-y-2">
                  <li>🥇 Microsoft AI Hackathon Winner (2025)</li>
                  <li>🌟 2x SUN Award Winner (ASU)</li>
                  <li>👑 Agile Leadership Award (FedEx)</li>
                  <li>🎓 4.0 GPA Master's Degree</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Experience Section */}
      <section id="experience" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Work Experience
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              My professional journey and the impact I've made in various roles.
            </p>
          </div>

          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-500 to-purple-600"></div>

            <div className="space-y-12">
              {/* Experience 1 - Current */}
              <div className="relative flex items-start group">
                <div className="absolute left-6 w-4 h-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full border-4 border-white dark:border-slate-900 group-hover:scale-125 transition-transform duration-300 animate-pulse"></div>
                <div className="ml-16 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-slate-800 dark:to-slate-700 p-6 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 w-full border border-blue-200 dark:border-blue-800">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Senior Software Engineer</h3>
                      <p className="text-blue-600 dark:text-blue-400 font-medium">CSAA Insurance Group (via Acxhange LLC)</p>
                      <p className="text-sm text-slate-500 dark:text-slate-400">Team: Digital Payments Transformation</p>
                    </div>
                    <div className="text-slate-500 dark:text-slate-400 text-sm mt-2 md:mt-0 flex items-center">
                      <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs mr-2">Current</span>
                      Feb 2025 - Present
                    </div>
                  </div>
                  <p className="text-slate-600 dark:text-slate-300 mb-4">
                    Leading digital payments transformation initiatives at CSAA Insurance Group, focusing on modernizing payment systems and enhancing customer experience.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">Payments</span>
                    <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm">Digital Transformation</span>
                    <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm">Insurance Tech</span>
                  </div>
                </div>
              </div>

              {/* Experience 2 - American Express (Recent) */}
              <div className="relative flex items-start group">
                <div className="absolute left-6 w-4 h-4 bg-blue-600 rounded-full border-4 border-white dark:border-slate-900 group-hover:scale-125 transition-transform duration-300"></div>
                <div className="ml-16 bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 w-full">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Software Development Engineer</h3>
                      <p className="text-blue-600 dark:text-blue-400 font-medium">American Express (via BlueOriginITStaffing LLC)</p>
                    </div>
                    <div className="text-slate-500 dark:text-slate-400 text-sm mt-2 md:mt-0">
                      Jun 2024 - Jan 2025 · 8 mos
                    </div>
                  </div>
                  <p className="text-slate-600 dark:text-slate-300 mb-4">
                    Developed enterprise payment solutions using Java and Spring Boot, working on critical financial systems at American Express.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm">Java</span>
                    <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm">Spring Boot</span>
                    <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">REST APIs</span>
                    <span className="bg-emerald-100 dark:bg-emerald-900 text-emerald-800 dark:text-emerald-200 px-3 py-1 rounded-full text-sm">Payments</span>
                    <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm">Jenkins</span>
                    <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm">Agile</span>
                    <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm">AWS</span>
                  </div>
                </div>
              </div>

              {/* Experience 3 - American Express (Previous) */}
              <div className="relative flex items-start group">
                <div className="absolute left-6 w-4 h-4 bg-blue-500 rounded-full border-4 border-white dark:border-slate-900 group-hover:scale-125 transition-transform duration-300"></div>
                <div className="ml-16 bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 w-full">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Software Development Engineer</h3>
                      <p className="text-blue-600 dark:text-blue-400 font-medium">American Express (via BVIS)</p>
                    </div>
                    <div className="text-slate-500 dark:text-slate-400 text-sm mt-2 md:mt-0">
                      Aug 2023 - Mar 2024 · 8 mos
                    </div>
                  </div>
                  <div className="text-slate-600 dark:text-slate-300 mb-4 space-y-2">
                    <p>• Achieved 20% increase in project efficiency using Java, Python, Spring Boot, and Kafka</p>
                    <p>• Developed solutions with 95% defect detection rate and 20% performance improvement</p>
                    <p>• Reduced testing cycle time by 30% with CI/CD automation using Jenkins and Docker</p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm">Java</span>
                    <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">Python</span>
                    <span className="bg-emerald-100 dark:bg-emerald-900 text-emerald-800 dark:text-emerald-200 px-3 py-1 rounded-full text-sm">Spring Boot</span>
                    <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm">Apache Kafka</span>
                    <span className="bg-lime-100 dark:bg-lime-900 text-lime-800 dark:text-lime-200 px-3 py-1 rounded-full text-sm">Cypress.io</span>
                    <span className="bg-cyan-100 dark:bg-cyan-900 text-cyan-800 dark:text-cyan-200 px-3 py-1 rounded-full text-sm">SQL</span>
                    <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm">JavaScript</span>
                    <span className="bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-200 px-3 py-1 rounded-full text-sm">AWS</span>
                    <span className="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-3 py-1 rounded-full text-sm">JIRA</span>
                    <span className="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 px-3 py-1 rounded-full text-sm">Git</span>
                    <span className="bg-violet-100 dark:bg-violet-900 text-violet-800 dark:text-violet-200 px-3 py-1 rounded-full text-sm">Jenkins</span>
                    <span className="bg-slate-100 dark:bg-slate-900 text-slate-800 dark:text-slate-200 px-3 py-1 rounded-full text-sm">GitHub Actions</span>
                    <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm">Docker</span>
                    <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm">Kubernetes</span>
                    <span className="bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200 px-3 py-1 rounded-full text-sm">CI/CD</span>
                    <span className="bg-teal-100 dark:bg-teal-900 text-teal-800 dark:text-teal-200 px-3 py-1 rounded-full text-sm">Agile</span>
                    <span className="bg-rose-100 dark:bg-rose-900 text-rose-800 dark:text-rose-200 px-3 py-1 rounded-full text-sm">Selenium</span>
                    <span className="bg-sky-100 dark:bg-sky-900 text-sky-800 dark:text-sky-200 px-3 py-1 rounded-full text-sm">CloudWatch</span>
                  </div>
                </div>
              </div>

              {/* Experience 4 - EPICS Volunteer */}
              <div className="relative flex items-start group">
                <div className="absolute left-6 w-4 h-4 bg-green-600 rounded-full border-4 border-white dark:border-slate-900 group-hover:scale-125 transition-transform duration-300"></div>
                <div className="ml-16 bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 w-full">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Software Engineer (Volunteer)</h3>
                      <p className="text-green-600 dark:text-green-400 font-medium">EPICS at ASU</p>
                      <p className="text-sm text-slate-500 dark:text-slate-400">Team: DocYou - Phoenix Children's Hospital</p>
                    </div>
                    <div className="text-slate-500 dark:text-slate-400 text-sm mt-2 md:mt-0">
                      Apr 2023 - Jul 2023 · 4 mos
                    </div>
                  </div>
                  <div className="text-slate-600 dark:text-slate-300 mb-4 space-y-2">
                    <p>• Developed HIPAA-compliant software improving security by 30%</p>
                    <p>• Created 100+ REST API test cases, reducing manual testing by 50%</p>
                    <p>• Implemented ML algorithms improving diagnosis accuracy by 15%</p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm">MongoDB</span>
                    <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm">Firebase</span>
                    <span className="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-3 py-1 rounded-full text-sm">Jenkins</span>
                    <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm">AWS</span>
                    <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">Python</span>
                    <span className="bg-emerald-100 dark:bg-emerald-900 text-emerald-800 dark:text-emerald-200 px-3 py-1 rounded-full text-sm">Node.js</span>
                    <span className="bg-cyan-100 dark:bg-cyan-900 text-cyan-800 dark:text-cyan-200 px-3 py-1 rounded-full text-sm">React.js</span>
                    <span className="bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200 px-3 py-1 rounded-full text-sm">Cloud Firestore</span>
                    <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm">Machine Learning</span>
                    <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm">HIPAA</span>
                    <span className="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 px-3 py-1 rounded-full text-sm">Git</span>
                    <span className="bg-violet-100 dark:bg-violet-900 text-violet-800 dark:text-violet-200 px-3 py-1 rounded-full text-sm">REST APIs</span>
                  </div>
                </div>
              </div>

              {/* Experience 5 - AWS */}
              <div className="relative flex items-start group">
                <div className="absolute left-6 w-4 h-4 bg-orange-500 rounded-full border-4 border-white dark:border-slate-900 group-hover:scale-125 transition-transform duration-300"></div>
                <div className="ml-16 bg-gradient-to-br from-orange-50 to-yellow-50 dark:from-slate-800 dark:to-slate-700 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 w-full border border-orange-200 dark:border-orange-800">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Software Development Engineer</h3>
                      <p className="text-orange-600 dark:text-orange-400 font-medium">Amazon Web Services (AWS)</p>
                      <p className="text-sm text-slate-500 dark:text-slate-400">Team: Elastic Block Storage Volume Hydration</p>
                    </div>
                    <div className="text-slate-500 dark:text-slate-400 text-sm mt-2 md:mt-0">
                      Sep 2022 - Mar 2023 · 7 mos
                    </div>
                  </div>
                  <div className="text-slate-600 dark:text-slate-300 mb-4 space-y-2">
                    <p>• Enhanced system reliability by configuring 250 CloudWatch alarms</p>
                    <p>• Reduced deployment times by 40% and operational costs by 15% using Terraform</p>
                    <p>• Improved processing times by 20% with Apache Spark and CloudWatch</p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm">Java</span>
                    <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">Python</span>
                    <span className="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 px-3 py-1 rounded-full text-sm">Linux</span>
                    <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm">Terraform</span>
                    <span className="bg-cyan-100 dark:bg-cyan-900 text-cyan-800 dark:text-cyan-200 px-3 py-1 rounded-full text-sm">CloudWatch</span>
                    <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm">AWS S3</span>
                    <span className="bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200 px-3 py-1 rounded-full text-sm">CDN</span>
                    <span className="bg-emerald-100 dark:bg-emerald-900 text-emerald-800 dark:text-emerald-200 px-3 py-1 rounded-full text-sm">Bash</span>
                    <span className="bg-slate-100 dark:bg-slate-900 text-slate-800 dark:text-slate-200 px-3 py-1 rounded-full text-sm">Git</span>
                    <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm">Elastic Block Store</span>
                    <span className="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-3 py-1 rounded-full text-sm">Kanban</span>
                    <span className="bg-violet-100 dark:bg-violet-900 text-violet-800 dark:text-violet-200 px-3 py-1 rounded-full text-sm">Jenkins</span>
                    <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm">CI/CD</span>
                  </div>
                </div>
              </div>

              {/* Experience 6 - ASU QA */}
              <div className="relative flex items-start group">
                <div className="absolute left-6 w-4 h-4 bg-yellow-600 rounded-full border-4 border-white dark:border-slate-900 group-hover:scale-125 transition-transform duration-300"></div>
                <div className="ml-16 bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 w-full">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Quality Assurance Assistant</h3>
                      <p className="text-yellow-600 dark:text-yellow-400 font-medium">EdPlus at Arizona State University</p>
                      <p className="text-sm text-slate-500 dark:text-slate-400">🏆 SUN Award Winner (2021, 2022)</p>
                    </div>
                    <div className="text-slate-500 dark:text-slate-400 text-sm mt-2 md:mt-0">
                      Apr 2021 - Jul 2022 · 1 yr 4 mos
                    </div>
                  </div>
                  <div className="text-slate-600 dark:text-slate-300 mb-4 space-y-2">
                    <p>• Led QA optimization reducing manual testing by 85% using Cypress</p>
                    <p>• Established Selenium automation framework reducing regression time by 50%</p>
                    <p>• Performed end-to-end testing for Starbucks Global Academy</p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm">Cypress.io</span>
                    <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">Selenium</span>
                    <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm">Manual Testing</span>
                    <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm">Agile Methodologies</span>
                    <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm">Java</span>
                    <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm">JavaScript</span>
                    <span className="bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200 px-3 py-1 rounded-full text-sm">BrowserStack</span>
                    <span className="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-3 py-1 rounded-full text-sm">JIRA</span>
                    <span className="bg-cyan-100 dark:bg-cyan-900 text-cyan-800 dark:text-cyan-200 px-3 py-1 rounded-full text-sm">CI/CD</span>
                  </div>
                </div>
              </div>

              {/* Experience 7 - FedEx Technical Lead */}
              <div className="relative flex items-start group">
                <div className="absolute left-6 w-4 h-4 bg-purple-600 rounded-full border-4 border-white dark:border-slate-900 group-hover:scale-125 transition-transform duration-300"></div>
                <div className="ml-16 bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 w-full">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Technical Team Lead</h3>
                      <p className="text-purple-600 dark:text-purple-400 font-medium">FedEx (via Atos Syntel)</p>
                      <p className="text-sm text-slate-500 dark:text-slate-400">Team: FedEx Reward Account Management</p>
                    </div>
                    <div className="text-slate-500 dark:text-slate-400 text-sm mt-2 md:mt-0">
                      Oct 2019 - Oct 2020 · 1 yr 1 mo
                    </div>
                  </div>
                  <div className="text-slate-600 dark:text-slate-300 mb-4 space-y-2">
                    <p>• Led high-performing team elevating software quality by 30%</p>
                    <p>• Boosted CI/CD pipeline efficiency by 25% using Jenkins</p>
                    <p>• Achieved 100% code coverage and received Agile leadership award</p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">JIRA</span>
                    <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm">Jenkins</span>
                    <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm">Test Automation Frameworks</span>
                    <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm">Agile Leadership</span>
                    <span className="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-3 py-1 rounded-full text-sm">Team Leadership</span>
                    <span className="bg-cyan-100 dark:bg-cyan-900 text-cyan-800 dark:text-cyan-200 px-3 py-1 rounded-full text-sm">SQL</span>
                    <span className="bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200 px-3 py-1 rounded-full text-sm">Project Management</span>
                    <span className="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 px-3 py-1 rounded-full text-sm">Git</span>
                  </div>
                </div>
              </div>

              {/* Experience 8 - FedEx Full Stack */}
              <div className="relative flex items-start group">
                <div className="absolute left-6 w-4 h-4 bg-indigo-600 rounded-full border-4 border-white dark:border-slate-900 group-hover:scale-125 transition-transform duration-300"></div>
                <div className="ml-16 bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 w-full">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Full Stack Engineer</h3>
                      <p className="text-indigo-600 dark:text-indigo-400 font-medium">FedEx (via Atos Syntel)</p>
                      <p className="text-sm text-slate-500 dark:text-slate-400">Team: FedEx Enterprise Security Center</p>
                    </div>
                    <div className="text-slate-500 dark:text-slate-400 text-sm mt-2 md:mt-0">
                      May 2018 - Sep 2019 · 1 yr 5 mos
                    </div>
                  </div>
                  <div className="text-slate-600 dark:text-slate-300 mb-4 space-y-2">
                    <p>• Pioneered responsive applications enhancing user experience by 30%</p>
                    <p>• Optimized test automation boosting coverage by 40% using Selenium</p>
                    <p>• Deployed updates across 12+ environments in under 7 minutes</p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm">Core Java</span>
                    <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">Python</span>
                    <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm">Selenium</span>
                    <span className="bg-emerald-100 dark:bg-emerald-900 text-emerald-800 dark:text-emerald-200 px-3 py-1 rounded-full text-sm">Spring Boot</span>
                    <span className="bg-lime-100 dark:bg-lime-900 text-lime-800 dark:text-lime-200 px-3 py-1 rounded-full text-sm">Cucumber</span>
                    <span className="bg-cyan-100 dark:bg-cyan-900 text-cyan-800 dark:text-cyan-200 px-3 py-1 rounded-full text-sm">React.js</span>
                    <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm">AWS</span>
                    <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm">Software Testing</span>
                    <span className="bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200 px-3 py-1 rounded-full text-sm">Test Automation</span>
                    <span className="bg-rose-100 dark:bg-rose-900 text-rose-800 dark:text-rose-200 px-3 py-1 rounded-full text-sm">Manual Testing</span>
                    <span className="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-3 py-1 rounded-full text-sm">Jenkins</span>
                    <span className="bg-violet-100 dark:bg-violet-900 text-violet-800 dark:text-violet-200 px-3 py-1 rounded-full text-sm">Agile</span>
                    <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm">CI/CD</span>
                    <span className="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 px-3 py-1 rounded-full text-sm">Git</span>
                  </div>
                </div>
              </div>

              {/* Experience 9 - Scientific Games */}
              <div className="relative flex items-start group">
                <div className="absolute left-6 w-4 h-4 bg-pink-600 rounded-full border-4 border-white dark:border-slate-900 group-hover:scale-125 transition-transform duration-300"></div>
                <div className="ml-16 bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 w-full">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Software Quality Assurance</h3>
                      <p className="text-pink-600 dark:text-pink-400 font-medium">Scientific Games</p>
                      <p className="text-sm text-slate-500 dark:text-slate-400">Internship • Gaming Industry</p>
                    </div>
                    <div className="text-slate-500 dark:text-slate-400 text-sm mt-2 md:mt-0">
                      May 2016 - May 2018 · 2 yrs 1 mo
                    </div>
                  </div>
                  <div className="text-slate-600 dark:text-slate-300 mb-4 space-y-2">
                    <p>• Worked with C# and Unity for game testing and development</p>
                    <p>• Developed G2S Events, Meters, Commands handling features</p>
                    <p>• Performed manual and automated testing across multiple platforms</p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm">Selenium</span>
                    <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm">Appium</span>
                    <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm">C#</span>
                    <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">HTML</span>
                    <span className="bg-cyan-100 dark:bg-cyan-900 text-cyan-800 dark:text-cyan-200 px-3 py-1 rounded-full text-sm">CSS</span>
                    <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm">JavaScript</span>
                    <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm">Manual Testing</span>
                    <span className="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 px-3 py-1 rounded-full text-sm">Git</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Education Section */}
      <section id="education" className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-900">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
              🎓 Education
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              My academic background and achievements. 📚
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Master's Degree */}
            <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-md">
              <div className="mb-4">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Master's in Information Technology</h3>
                <p className="text-blue-600 dark:text-blue-400 font-medium">Arizona State University</p>
                <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded text-xs font-medium">4.0 GPA</span>
              </div>
              <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">
                Advanced studies in Information Technology with perfect academic performance.
                Specialized in enterprise systems, cloud computing, and software architecture.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">Enterprise Systems</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">Cloud Computing</span>
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-xs">Software Architecture</span>
              </div>
            </div>

            {/* Bachelor's Degree */}
            <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-md">
              <div className="mb-4">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white">BTech in Computer Engineering</h3>
                <p className="text-blue-600 dark:text-blue-400 font-medium">IIITDM Kancheepuram</p>
                <p className="text-xs text-slate-500 dark:text-slate-400">Indian Institute of Information Technology</p>
              </div>
              <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">
                Comprehensive engineering education in Computer Engineering from a prestigious IIIT.
                Strong foundation in computer science fundamentals and programming.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">Computer Engineering</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">Programming</span>
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-xs">Engineering</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Achievements & Awards Section */}
      <section id="achievements" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Achievements & Awards
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Recognition for excellence, leadership, and outstanding contributions throughout my career.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Microsoft AI Hackathon - Latest Achievement */}
            <div className="group bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 border-2 border-blue-300 dark:border-blue-700 relative overflow-hidden">
              {/* New badge */}
              <div className="absolute top-4 right-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-3 py-1 rounded-full text-xs font-bold animate-pulse">
                NEW
              </div>
              <div className="text-center mb-6">
                <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Best Python Agent</h3>
                <div className="mb-4">
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium">2025</span>
                </div>
              </div>
              <div className="space-y-3">
                <p className="text-slate-600 dark:text-slate-300 text-center">
                  <strong>Microsoft AI Agents Hackathon</strong>
                </p>
                <p className="text-slate-500 dark:text-slate-400 text-sm text-center">
                  Konveyor - AI Powered Knowledge Transfer Agent
                </p>
                <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                  <p className="text-xs text-slate-600 dark:text-slate-300 text-center">
                    "Solving onboarding challenges with AI-powered knowledge transfer and tribal knowledge management"
                  </p>
                </div>
                <div className="mt-4 flex justify-center">
                  <a
                    href="https://techcommunity.microsoft.com/blog/azuredevcommunityblog/ai-agents-hackathon-2025-%E2%80%93-category-winners-showcase/4415088"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium transition-colors duration-200"
                  >
                    View Announcement
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            {/* SUN Awards */}
            <div className="group bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-slate-800 dark:to-slate-700 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3">
              <div className="text-center mb-6">
                <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">SUN Awards (2x)</h3>
                <div className="mb-4 space-y-1">
                  <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm font-medium block">2021</span>
                  <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm font-medium block">2022</span>
                </div>
              </div>
              <div className="space-y-3">
                <p className="text-slate-600 dark:text-slate-300 text-center">
                  <strong>Arizona State University</strong>
                </p>
                <p className="text-slate-500 dark:text-slate-400 text-sm text-center">
                  EdPlus Quality Assurance Excellence
                </p>
                <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg">
                  <p className="text-xs text-slate-600 dark:text-slate-300 text-center">
                    "Outstanding performance in QA automation and process improvement"
                  </p>
                </div>
              </div>
            </div>

            {/* Agile Leadership Award */}
            <div className="group bg-gradient-to-br from-purple-50 to-pink-50 dark:from-slate-800 dark:to-slate-700 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3">
              <div className="text-center mb-6">
                <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Agile Leadership</h3>
                <div className="mb-4">
                  <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm font-medium">2020</span>
                </div>
              </div>
              <div className="space-y-3">
                <p className="text-slate-600 dark:text-slate-300 text-center">
                  <strong>FedEx Corporation</strong>
                </p>
                <p className="text-slate-500 dark:text-slate-400 text-sm text-center">
                  Team Leadership Excellence
                </p>
                <div className="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-lg">
                  <p className="text-xs text-slate-600 dark:text-slate-300 text-center">
                    "Exceptional leadership in agile methodologies and team management"
                  </p>
                </div>
              </div>
            </div>

            {/* Academic Excellence */}
            <div className="group bg-gradient-to-br from-green-50 to-emerald-50 dark:from-slate-800 dark:to-slate-700 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3">
              <div className="text-center mb-6">
                <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-green-600 to-emerald-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Academic Excellence</h3>
                <div className="mb-4">
                  <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">4.0 GPA</span>
                </div>
              </div>
              <div className="space-y-3">
                <p className="text-slate-600 dark:text-slate-300 text-center">
                  <strong>Master's Degree</strong>
                </p>
                <p className="text-slate-500 dark:text-slate-400 text-sm text-center">
                  Information Technology - ASU
                </p>
                <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg">
                  <p className="text-xs text-slate-600 dark:text-slate-300 text-center">
                    "Perfect academic performance throughout graduate studies"
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section id="skills" className="py-16 px-4 sm:px-6 lg:px-8 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
              🛠️ Skills & Technologies
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Technologies and tools I work with to build innovative solutions. ⚡
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-6">
            {/* Programming Languages */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">💻 Programming</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Java</span>
                  <span className="text-blue-600 dark:text-blue-400 font-medium">Expert</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Python</span>
                  <span className="text-blue-600 dark:text-blue-400 font-medium">Expert</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">JavaScript</span>
                  <span className="text-blue-600 dark:text-blue-400 font-medium">Advanced</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">C#</span>
                  <span className="text-blue-600 dark:text-blue-400 font-medium">Intermediate</span>
                </div>
              </div>
            </div>

            {/* Frameworks & Libraries */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">🔧 Frameworks</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Spring Boot</span>
                  <span className="text-green-600 dark:text-green-400 font-medium">Expert</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">React.js</span>
                  <span className="text-green-600 dark:text-green-400 font-medium">Advanced</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Node.js</span>
                  <span className="text-green-600 dark:text-green-400 font-medium">Advanced</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Apache Kafka</span>
                  <span className="text-green-600 dark:text-green-400 font-medium">Advanced</span>
                </div>
              </div>
            </div>

            {/* Cloud & DevOps */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">☁️ Cloud & DevOps</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">AWS</span>
                  <span className="text-purple-600 dark:text-purple-400 font-medium">Expert</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Docker</span>
                  <span className="text-purple-600 dark:text-purple-400 font-medium">Advanced</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Kubernetes</span>
                  <span className="text-purple-600 dark:text-purple-400 font-medium">Advanced</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Jenkins</span>
                  <span className="text-purple-600 dark:text-purple-400 font-medium">Advanced</span>
                </div>
              </div>
            </div>

            {/* Testing & QA */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">🧪 Testing & QA</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Selenium</span>
                  <span className="text-orange-600 dark:text-orange-400 font-medium">Expert</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Cypress.io</span>
                  <span className="text-orange-600 dark:text-orange-400 font-medium">Advanced</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Appium</span>
                  <span className="text-orange-600 dark:text-orange-400 font-medium">Advanced</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">BrowserStack</span>
                  <span className="text-orange-600 dark:text-orange-400 font-medium">Intermediate</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="py-20 px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-900">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Featured Projects
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              A showcase of innovative solutions and technical achievements across various domains.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Project 1 - VCT Hackathon */}
            <div className="group bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:-rotate-1 border border-blue-200 dark:border-blue-800">
              <div className="relative h-48 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 flex items-center justify-center overflow-hidden">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                <div className="relative z-10 text-center">
                  <div className="w-16 h-16 mx-auto mb-3 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                  <span className="text-white text-xl font-bold">VCT Hackathon</span>
                </div>
                <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="bg-white/20 backdrop-blur-sm rounded-full p-2">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <div className="mb-3">
                  <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-1 rounded-full text-xs font-bold">🏆 Microsoft Winner</span>
                </div>
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                  VALORANT Team Optimizer
                </h3>
                <p className="text-slate-600 dark:text-slate-300 mb-4 leading-relaxed">
                  AI-powered digital assistant for VALORANT esports team optimization using AWS, OpenSearch, and vector databases with RAG architecture.
                </p>
                <div className="flex flex-wrap gap-2 mb-6">
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Python</span>
                  <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">AWS</span>
                  <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">OpenSearch</span>
                  <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Vector DB</span>
                </div>
                <div className="flex items-center justify-between">
                  <a
                    href="https://github.com/neeharve/VCT_Hackathon"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors duration-200"
                  >
                    View Project
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                  <div className="text-yellow-500">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Project 2 - Cloud Inventory Management */}
            <div className="group bg-white dark:bg-slate-800 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:-rotate-1">
              <div className="relative h-48 bg-gradient-to-br from-green-500 via-teal-500 to-blue-600 flex items-center justify-center overflow-hidden">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                <div className="relative z-10 text-center">
                  <div className="w-16 h-16 mx-auto mb-3 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="text-white text-xl font-bold">Inventory System</span>
                </div>
                <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="bg-white/20 backdrop-blur-sm rounded-full p-2">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">
                  Cloud Inventory Management
                </h3>
                <p className="text-slate-600 dark:text-slate-300 mb-4 leading-relaxed">
                  Enterprise inventory system with ML-powered demand forecasting, built on AWS with React frontend.
                </p>
                <div className="flex flex-wrap gap-2 mb-6">
                  <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">AWS</span>
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Java</span>
                  <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">React</span>
                  <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">ML Forecasting</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-500 dark:text-slate-400 text-sm font-medium">Enterprise Project</span>
                  <div className="text-green-500">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Project 3 - Cruddr */}
            <div className="group bg-white dark:bg-slate-800 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:rotate-1">
              <div className="relative h-48 bg-gradient-to-br from-purple-500 via-pink-500 to-red-500 flex items-center justify-center overflow-hidden">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                <div className="relative z-10 text-center">
                  <div className="w-16 h-16 mx-auto mb-3 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="text-white text-xl font-bold">Cruddr Platform</span>
                </div>
                <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="bg-white/20 backdrop-blur-sm rounded-full p-2">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300">
                  Cruddr - Micro-blogging Platform
                </h3>
                <p className="text-slate-600 dark:text-slate-300 mb-4 leading-relaxed">
                  Production-ready micro-blogging platform with 25% cost reduction and 30% performance improvement using AWS Fargate.
                </p>
                <div className="flex flex-wrap gap-2 mb-6">
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">React</span>
                  <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Python Flask</span>
                  <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">AWS Fargate</span>
                  <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Docker</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-500 dark:text-slate-400 text-sm font-medium">Production App</span>
                  <div className="flex items-center space-x-1">
                    <span className="text-green-600 dark:text-green-400 text-sm font-medium">25% Cost ↓</span>
                    <div className="text-purple-500">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Healthcare Data Pipeline */}
            <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3">
                Healthcare Data Pipeline
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Automated data processing pipeline for healthcare analytics with Azure and Python.
              </p>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm">Python</span>
                <span className="bg-cyan-100 dark:bg-cyan-900 text-cyan-800 dark:text-cyan-200 px-2 py-1 rounded text-sm">Azure</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-sm">Databricks</span>
              </div>
              <span className="text-slate-500 dark:text-slate-400 text-sm italic">Healthcare Analytics</span>
            </div>

            {/* AR Tourism */}
            <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3">
                AR Tourism App
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Augmented reality application for tourism with interactive map views and points of interest.
              </p>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-2 py-1 rounded text-sm">Swift</span>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm">Dart</span>
                <span className="bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200 px-2 py-1 rounded text-sm">AR</span>
              </div>
              <span className="text-slate-500 dark:text-slate-400 text-sm italic">ASU Academic Project</span>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-16 px-4 sm:px-6 lg:px-8 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
              📬 Get In Touch
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              I'm always open to discussing new opportunities, interesting projects, or just having a chat about technology. 💬
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <div className="mb-4">
                <svg className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
              </div>
              <h3 className="font-semibold text-slate-900 dark:text-white mb-2">📧 Email</h3>
              <p className="text-slate-600 dark:text-slate-300 text-sm"><EMAIL></p>
              <p className="text-slate-600 dark:text-slate-300 text-sm"><EMAIL></p>
            </div>

            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <div className="mb-4">
                <svg className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="font-semibold text-slate-900 dark:text-white mb-2">💼 LinkedIn</h3>
              <a href="https://www.linkedin.com/in/neeharika-vemulapati" className="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                linkedin.com/in/neeharika-vemulapati
              </a>
            </div>

            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <div className="mb-4">
                <svg className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="font-semibold text-slate-900 dark:text-white mb-2">🐙 GitHub</h3>
              <a href="https://github.com/neeharve" className="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                github.com/neeharve
              </a>
            </div>
          </div>

        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 dark:bg-black text-white py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto text-center">
          <p className="text-slate-300 mb-2">
            © 2025 Neeharika Vemulapati. All rights reserved.
          </p>
          <p className="text-slate-400 text-sm">
            Built with Next.js and Tailwind CSS
          </p>
        </div>
      </footer>
    </div>
  );
}
